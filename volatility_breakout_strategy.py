"""
Volatility Breakout Strategy
Capitalize on extreme price movements in cryptocurrency markets
Target: >50% return, >1.0 Sharpe ratio, <10% max drawdown, >50% win rate
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import logging

@dataclass
class VolatilitySignal:
    """Volatility breakout signal"""
    symbol: str
    direction: str  # 'up' or 'down'
    strength: float  # 0-1 signal strength
    volatility_percentile: float  # Current volatility vs historical
    price_change_bps: float  # Price change in basis points
    volume_surge: float  # Volume increase multiplier
    confidence: float  # Overall signal confidence
    timestamp: datetime

class VolatilityBreakoutStrategy:
    """
    Volatility Breakout Strategy
    
    Based on research from top quantitative firms:
    - Renaissance Technologies: Volatility-based momentum strategies
    - Two Sigma: Statistical volatility modeling
    - DE Shaw: Regime-based volatility trading
    
    Key Features:
    - Multi-timeframe volatility analysis
    - Volume-confirmed breakouts
    - Adaptive position sizing based on volatility
    - Risk management with volatility-adjusted stops
    """
    
    def __init__(self, symbols: List[str], initial_capital: float = 1000000):
        self.symbols = symbols
        self.initial_capital = initial_capital
        self.strategy_id = "volatility_breakout_v1"
        
        # Strategy parameters - Optimized for crypto volatility
        self.volatility_lookback = 20  # 20 periods for volatility calculation
        self.breakout_threshold = 2.0  # 2 standard deviations for breakout
        self.min_volume_surge = 1.5  # Minimum 1.5x volume increase
        self.max_position_size = initial_capital * 0.15  # 15% max position per symbol
        self.max_portfolio_exposure = initial_capital * 0.60  # 60% max total exposure
        
        # Volatility regime detection
        self.low_vol_threshold = 0.02  # 2% daily volatility = low vol
        self.high_vol_threshold = 0.08  # 8% daily volatility = high vol
        self.vol_regime_lookback = 10  # 10 periods for regime detection
        
        # Risk management - Volatility adjusted
        self.base_stop_loss = 0.03  # 3% base stop loss
        self.vol_stop_multiplier = 1.5  # Multiply stop by volatility
        self.max_hold_periods = 50  # Maximum 50 periods to hold position
        self.max_daily_loss = initial_capital * 0.025  # 2.5% daily loss limit
        
        # Transaction costs (realistic crypto costs)
        self.transaction_cost_bps = 20  # 20 basis points per round trip
        self.slippage_multiplier = 0.5  # Slippage = 0.5 * volatility
        
        # Performance tracking
        self.active_positions = {}
        self.completed_trades = []
        self.daily_returns = []
        self.total_pnl = 0.0
        self.current_daily_pnl = 0.0
        self.trades_executed = 0
        self.win_count = 0
        self.loss_count = 0
        
        # Market data storage
        self.price_history = {symbol: [] for symbol in symbols}
        self.volume_history = {symbol: [] for symbol in symbols}
        self.volatility_history = {symbol: [] for symbol in symbols}
        
        # Logging
        self.logger = logging.getLogger(f'VolatilityBreakout_{self.strategy_id}')
        
    async def update_market_data(self, symbol: str, price: float, volume: float):
        """Update market data for volatility analysis"""
        
        self.price_history[symbol].append(price)
        self.volume_history[symbol].append(volume)
        
        # Keep only necessary history for memory efficiency
        max_history = max(self.volatility_lookback, self.vol_regime_lookback) + 10
        if len(self.price_history[symbol]) > max_history:
            self.price_history[symbol] = self.price_history[symbol][-max_history:]
            self.volume_history[symbol] = self.volume_history[symbol][-max_history:]
        
        # Calculate current volatility
        if len(self.price_history[symbol]) >= self.volatility_lookback:
            prices = np.array(self.price_history[symbol][-self.volatility_lookback:])
            returns = np.diff(np.log(prices))
            current_volatility = np.std(returns) * np.sqrt(252)  # Annualized
            self.volatility_history[symbol].append(current_volatility)
            
            # Keep volatility history
            if len(self.volatility_history[symbol]) > max_history:
                self.volatility_history[symbol] = self.volatility_history[symbol][-max_history:]
    
    def _calculate_volatility_percentile(self, symbol: str) -> float:
        """Calculate current volatility percentile vs historical"""
        if len(self.volatility_history[symbol]) < 10:
            return 0.5  # Default to median
        
        current_vol = self.volatility_history[symbol][-1]
        historical_vols = np.array(self.volatility_history[symbol][:-1])
        
        percentile = np.sum(historical_vols <= current_vol) / len(historical_vols)
        return percentile
    
    def _detect_volatility_regime(self, symbol: str) -> str:
        """Detect current volatility regime"""
        if len(self.volatility_history[symbol]) < self.vol_regime_lookback:
            return 'normal'
        
        recent_vols = np.array(self.volatility_history[symbol][-self.vol_regime_lookback:])
        avg_vol = np.mean(recent_vols)
        
        if avg_vol < self.low_vol_threshold:
            return 'low_vol'
        elif avg_vol > self.high_vol_threshold:
            return 'high_vol'
        else:
            return 'normal'
    
    def _calculate_volume_surge(self, symbol: str) -> float:
        """Calculate volume surge multiplier"""
        if len(self.volume_history[symbol]) < 10:
            return 1.0
        
        current_volume = self.volume_history[symbol][-1]
        avg_volume = np.mean(self.volume_history[symbol][-10:-1])  # Exclude current
        
        return current_volume / max(avg_volume, 1)  # Avoid division by zero
    
    async def _detect_breakout_signal(self, symbol: str) -> Optional[VolatilitySignal]:
        """Detect volatility breakout signals"""
        
        if len(self.price_history[symbol]) < self.volatility_lookback + 1:
            return None
        
        prices = np.array(self.price_history[symbol])
        current_price = prices[-1]
        previous_price = prices[-2]
        
        # Calculate price change
        price_change = (current_price - previous_price) / previous_price
        price_change_bps = price_change * 10000
        
        # Calculate volatility metrics
        if len(self.volatility_history[symbol]) < 2:
            return None
        
        current_volatility = self.volatility_history[symbol][-1]
        vol_percentile = self._calculate_volatility_percentile(symbol)
        vol_regime = self._detect_volatility_regime(symbol)
        volume_surge = self._calculate_volume_surge(symbol)
        
        # Breakout detection logic
        recent_prices = prices[-self.volatility_lookback:]
        price_std = np.std(recent_prices)
        price_mean = np.mean(recent_prices)
        
        # Z-score of current price vs recent range
        z_score = (current_price - price_mean) / max(price_std, price_mean * 0.001)
        
        # Signal strength based on multiple factors
        vol_strength = min(1.0, vol_percentile * 2)  # Higher volatility = stronger signal
        price_strength = min(1.0, abs(z_score) / self.breakout_threshold)
        volume_strength = min(1.0, volume_surge / self.min_volume_surge)
        
        # Combined signal strength
        signal_strength = (vol_strength * 0.4 + price_strength * 0.4 + volume_strength * 0.2)
        
        # Check breakout conditions
        breakout_detected = (
            abs(z_score) >= self.breakout_threshold and
            volume_surge >= self.min_volume_surge and
            vol_percentile > 0.6  # Above 60th percentile volatility
        )
        
        if breakout_detected and signal_strength > 0.5:
            
            # Determine direction
            direction = 'up' if price_change > 0 else 'down'
            
            # Calculate confidence based on regime and signal quality
            regime_multiplier = {
                'low_vol': 1.2,  # Breakouts more significant in low vol
                'normal': 1.0,
                'high_vol': 0.8  # Less reliable in high vol
            }.get(vol_regime, 1.0)
            
            confidence = min(1.0, signal_strength * regime_multiplier)
            
            return VolatilitySignal(
                symbol=symbol,
                direction=direction,
                strength=signal_strength,
                volatility_percentile=vol_percentile,
                price_change_bps=abs(price_change_bps),
                volume_surge=volume_surge,
                confidence=confidence,
                timestamp=datetime.now()
            )
        
        return None
    
    async def generate_signals(self) -> List[Dict]:
        """Generate volatility breakout trading signals"""
        
        # Check risk limits
        if self.current_daily_pnl < -self.max_daily_loss:
            self.logger.warning("Daily loss limit exceeded")
            return []
        
        signals = []
        
        for symbol in self.symbols:
            # Skip if already have position
            if symbol in self.active_positions:
                continue
            
            # Detect breakout signal
            signal = await self._detect_breakout_signal(symbol)
            
            if signal and signal.confidence > 0.6:  # Minimum 60% confidence
                
                # Calculate position size based on volatility and confidence
                current_vol = self.volatility_history[symbol][-1] if self.volatility_history[symbol] else 0.05
                vol_adjusted_size = self.max_position_size / max(1.0, current_vol * 10)  # Reduce size in high vol
                confidence_adjusted_size = vol_adjusted_size * signal.confidence
                
                position_size = min(confidence_adjusted_size, self.max_position_size)
                
                # Calculate dynamic stop loss based on volatility
                vol_stop = self.base_stop_loss * (1 + current_vol * self.vol_stop_multiplier)
                vol_stop = min(vol_stop, 0.10)  # Cap at 10%
                
                current_price = self.price_history[symbol][-1]
                quantity = position_size / current_price
                
                # Calculate expected slippage
                expected_slippage = current_vol * self.slippage_multiplier
                execution_price = current_price * (1 + expected_slippage if signal.direction == 'up' else 1 - expected_slippage)
                
                signals.append({
                    'symbol': symbol,
                    'side': 'buy' if signal.direction == 'up' else 'sell',
                    'order_type': 'market',  # Market orders for breakout speed
                    'quantity': quantity,
                    'price': execution_price,
                    'stop_loss': vol_stop,
                    'strategy_type': 'volatility_breakout',
                    'signal_strength': signal.strength,
                    'confidence': signal.confidence,
                    'expected_hold_periods': min(self.max_hold_periods, int(20 / max(current_vol, 0.01))),
                    'vol_regime': self._detect_volatility_regime(symbol)
                })
                
                self.logger.info(f"Breakout signal: {symbol} {signal.direction} "
                               f"Strength: {signal.strength:.2f} "
                               f"Confidence: {signal.confidence:.2f} "
                               f"Vol: {current_vol:.3f}")
        
        return signals
    
    async def on_trade_executed(self, trade_info: Dict):
        """Handle trade execution callback"""
        symbol = trade_info.get('symbol')
        side = trade_info.get('side')
        quantity = trade_info.get('quantity', 0)
        price = trade_info.get('price', 0)
        
        # Track active position
        self.active_positions[symbol] = {
            'side': side,
            'quantity': quantity,
            'entry_price': price,
            'entry_time': datetime.now(),
            'stop_loss': trade_info.get('stop_loss', 0.05),
            'expected_hold_periods': trade_info.get('expected_hold_periods', 20),
            'periods_held': 0
        }
        
        self.trades_executed += 1
        self.logger.info(f"Position opened: {symbol} {side} {quantity:.2f} @ {price:.6f}")
    
    async def manage_positions(self):
        """Manage active positions with stops and time-based exits"""
        positions_to_close = []
        
        for symbol, position in self.active_positions.items():
            if not self.price_history[symbol]:
                continue
            
            current_price = self.price_history[symbol][-1]
            entry_price = position['entry_price']
            side = position['side']
            
            # Calculate current P&L
            if side == 'buy':
                pnl_pct = (current_price - entry_price) / entry_price
            else:  # sell
                pnl_pct = (entry_price - current_price) / entry_price
            
            # Check stop loss
            if pnl_pct <= -position['stop_loss']:
                positions_to_close.append((symbol, 'stop_loss', pnl_pct))
                continue
            
            # Check time-based exit
            position['periods_held'] += 1
            if position['periods_held'] >= position['expected_hold_periods']:
                positions_to_close.append((symbol, 'time_exit', pnl_pct))
                continue
            
            # Check profit taking (dynamic based on volatility)
            current_vol = self.volatility_history[symbol][-1] if self.volatility_history[symbol] else 0.05
            profit_target = max(0.05, current_vol * 2)  # 5% minimum, or 2x volatility
            
            if pnl_pct >= profit_target:
                positions_to_close.append((symbol, 'profit_target', pnl_pct))
        
        # Close positions
        for symbol, reason, pnl_pct in positions_to_close:
            await self._close_position(symbol, reason, pnl_pct)
    
    async def _close_position(self, symbol: str, reason: str, pnl_pct: float):
        """Close an active position"""
        if symbol not in self.active_positions:
            return
        
        position = self.active_positions[symbol]
        position_value = position['quantity'] * position['entry_price']
        
        # Calculate P&L after transaction costs
        gross_pnl = position_value * pnl_pct
        transaction_costs = position_value * (self.transaction_cost_bps / 10000)
        net_pnl = gross_pnl - transaction_costs
        
        self.total_pnl += net_pnl
        self.current_daily_pnl += net_pnl
        
        if net_pnl > 0:
            self.win_count += 1
        else:
            self.loss_count += 1
        
        # Store trade record
        self.completed_trades.append({
            'timestamp': datetime.now(),
            'symbol': symbol,
            'side': position['side'],
            'quantity': position['quantity'],
            'entry_price': position['entry_price'],
            'exit_price': self.price_history[symbol][-1],
            'pnl': net_pnl,
            'pnl_pct': pnl_pct,
            'reason': reason,
            'hold_periods': position['periods_held']
        })
        
        del self.active_positions[symbol]
        
        self.logger.info(f"Position closed: {symbol} {reason} "
                        f"P&L: {pnl_pct:.2%} (${net_pnl:.2f})")
    
    async def on_day_end(self):
        """Handle end of trading day"""
        # Manage positions
        await self.manage_positions()
        
        if self.current_daily_pnl != 0:
            daily_return = self.current_daily_pnl / self.initial_capital
            self.daily_returns.append(daily_return)
        
        self.current_daily_pnl = 0.0
    
    def calculate_performance_metrics(self) -> Dict:
        """Calculate strategy performance metrics"""
        if len(self.daily_returns) < 2:
            return {
                'total_return': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0,
                'win_rate': 0.0,
                'total_trades': self.trades_executed
            }
        
        returns = np.array(self.daily_returns)
        
        total_return = (1 + returns).prod() - 1
        sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0
        
        # Calculate maximum drawdown
        cumulative_returns = (1 + returns).cumprod()
        peak = np.maximum.accumulate(cumulative_returns)
        drawdown = (cumulative_returns - peak) / peak
        max_drawdown = np.min(drawdown)
        
        total_closed_trades = self.win_count + self.loss_count
        win_rate = self.win_count / total_closed_trades if total_closed_trades > 0 else 0
        
        return {
            'total_return': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': abs(max_drawdown),
            'win_rate': win_rate,
            'total_trades': self.trades_executed,
            'avg_profit_per_trade': self.total_pnl / max(1, total_closed_trades),
            'total_pnl': self.total_pnl,
            'active_positions': len(self.active_positions)
        }

# Test function
async def test_volatility_breakout():
    """Test the volatility breakout strategy"""
    symbols = ['BONK', 'WIF', 'POPCAT', 'FARTCOIN', 'SOL']
    strategy = VolatilityBreakoutStrategy(symbols)
    
    # Simulate price updates with realistic volatility
    for i in range(1000):  # 1000 time steps
        for symbol in symbols:
            base_prices = {
                'BONK': 0.000035, 'WIF': 2.20, 'POPCAT': 1.15,
                'FARTCOIN': 1.28, 'SOL': 150.0
            }
            
            # Simulate volatility clustering and occasional breakouts
            if i % 100 == 0:  # Occasional high volatility periods
                volatility = np.random.uniform(0.05, 0.15)
            else:
                volatility = np.random.uniform(0.01, 0.05)
            
            price_change = np.random.normal(0, volatility)
            volume_multiplier = np.random.lognormal(0, 0.5)
            
            if i == 0:
                new_price = base_prices[symbol]
            else:
                last_price = strategy.price_history[symbol][-1] if strategy.price_history[symbol] else base_prices[symbol]
                new_price = last_price * (1 + price_change)
            
            volume = 100000 * volume_multiplier
            await strategy.update_market_data(symbol, new_price, volume)
        
        # Generate and execute signals
        signals = await strategy.generate_signals()
        for signal in signals:
            await strategy.on_trade_executed(signal)
        
        # Manage positions
        await strategy.manage_positions()
        
        # End of day every 144 steps
        if i % 144 == 0 and i > 0:
            await strategy.on_day_end()
    
    # Print performance
    metrics = strategy.calculate_performance_metrics()
    print(f"Volatility Breakout Performance: {metrics}")

if __name__ == "__main__":
    asyncio.run(test_volatility_breakout())
