"""
Optimized Portfolio Strategy
Combines multiple strategies with risk-adjusted allocation to achieve target performance
Target: >50% return, >1.0 Sharpe ratio, <10% max drawdown, >50% win rate
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple
import logging

from statistical_arbitrage_strategy import StatisticalArbitrageStrategy
from market_making_strategy import MarketMakingStrategy
from high_frequency_momentum_strategy import HighFrequencyMomentumStrategy

class OptimizedPortfolioStrategy:
    """Optimized portfolio combining multiple strategies with risk management"""
    
    def __init__(self, symbols: List[str], initial_capital: float = 1000000):
        self.symbols = symbols
        self.initial_capital = initial_capital
        self.strategy_id = "optimized_portfolio_v1"
        
        # Portfolio allocation - Optimized based on backtest results
        self.allocations = {
            'statistical_arbitrage': 0.70,  # 70% to high-performing stat arb
            'market_making': 0.20,          # 20% to market making (if profitable)
            'hf_momentum': 0.10             # 10% to momentum (conservative)
        }
        
        # Initialize individual strategies with allocated capital
        self.stat_arb_strategy = StatisticalArbitrageStrategy(
            symbols=symbols, 
            initial_capital=initial_capital * self.allocations['statistical_arbitrage']
        )
        
        # Use more conservative parameters for stat arb in portfolio context
        self.stat_arb_strategy.entry_threshold = 2.5  # More conservative
        self.stat_arb_strategy.max_position_size = initial_capital * 0.02  # 2% per pair
        self.stat_arb_strategy.max_pairs = 4  # Limit pairs
        self.stat_arb_strategy.max_portfolio_exposure = initial_capital * 0.40  # 40% max exposure
        
        self.market_making_strategy = MarketMakingStrategy(
            symbols=symbols,
            initial_capital=initial_capital * self.allocations['market_making']
        )
        
        self.hf_momentum_strategy = HighFrequencyMomentumStrategy(
            symbols=symbols,
            initial_capital=initial_capital * self.allocations['hf_momentum']
        )
        
        # Portfolio-level risk management
        self.max_portfolio_drawdown = 0.10  # 10% max drawdown
        self.max_daily_loss = initial_capital * 0.02  # 2% daily loss limit
        self.current_daily_pnl = 0.0
        self.portfolio_value_history = [initial_capital]
        self.peak_portfolio_value = initial_capital
        
        # Performance tracking
        self.total_trades = 0
        self.winning_trades = 0
        self.daily_returns = []
        
        # Logging
        self.logger = logging.getLogger(f'OptimizedPortfolio_{self.strategy_id}')
        
    async def initialize(self, trading_engine=None):
        """Initialize all strategies"""
        if trading_engine:
            await self.stat_arb_strategy.initialize(trading_engine)
            await self.market_making_strategy.initialize(trading_engine)
            await self.hf_momentum_strategy.initialize(trading_engine)
        
        self.logger.info("Optimized Portfolio Strategy initialized")
        
    async def generate_signals(self) -> List[Dict]:
        """Generate signals from all strategies with portfolio-level risk management"""
        
        # Check portfolio-level risk limits
        current_drawdown = self._calculate_current_drawdown()
        if current_drawdown > self.max_portfolio_drawdown:
            self.logger.warning(f"Portfolio drawdown {current_drawdown:.2%} exceeds limit")
            return []
        
        if self.current_daily_pnl < -self.max_daily_loss:
            self.logger.warning(f"Daily loss limit exceeded: {self.current_daily_pnl:.2f}")
            return []
        
        all_signals = []
        
        # Get signals from Statistical Arbitrage (primary strategy)
        try:
            stat_arb_signals = await self.stat_arb_strategy.generate_signals()
            # Scale signals based on allocation
            for signal in stat_arb_signals:
                signal['quantity'] *= self.allocations['statistical_arbitrage']
                signal['portfolio_strategy'] = 'statistical_arbitrage'
            all_signals.extend(stat_arb_signals)
        except Exception as e:
            self.logger.error(f"Error in Statistical Arbitrage: {e}")
        
        # Get signals from Market Making (if profitable)
        try:
            mm_signals = await self.market_making_strategy.generate_signals()
            # Only use market making if it's showing positive performance
            if self._is_strategy_profitable('market_making'):
                for signal in mm_signals:
                    signal['quantity'] *= self.allocations['market_making']
                    signal['portfolio_strategy'] = 'market_making'
                all_signals.extend(mm_signals)
        except Exception as e:
            self.logger.error(f"Error in Market Making: {e}")
        
        # Get signals from HF Momentum (conservative allocation)
        try:
            momentum_signals = await self.hf_momentum_strategy.generate_signals()
            # Only use momentum if it's showing positive performance
            if self._is_strategy_profitable('hf_momentum'):
                for signal in momentum_signals:
                    signal['quantity'] *= self.allocations['hf_momentum']
                    signal['portfolio_strategy'] = 'hf_momentum'
                all_signals.extend(momentum_signals)
        except Exception as e:
            self.logger.error(f"Error in HF Momentum: {e}")
        
        # Apply portfolio-level position sizing limits
        all_signals = self._apply_portfolio_limits(all_signals)
        
        return all_signals
    
    def _calculate_current_drawdown(self) -> float:
        """Calculate current portfolio drawdown"""
        if len(self.portfolio_value_history) < 2:
            return 0.0
        
        current_value = self.portfolio_value_history[-1]
        self.peak_portfolio_value = max(self.peak_portfolio_value, current_value)
        
        if self.peak_portfolio_value == 0:
            return 0.0
        
        drawdown = (self.peak_portfolio_value - current_value) / self.peak_portfolio_value
        return drawdown
    
    def _is_strategy_profitable(self, strategy_name: str) -> bool:
        """Check if a strategy is currently profitable"""
        # Simple profitability check - can be enhanced with more sophisticated metrics
        if strategy_name == 'market_making':
            return self.market_making_strategy.total_pnl > 0
        elif strategy_name == 'hf_momentum':
            return self.hf_momentum_strategy.total_pnl > 0
        return True  # Statistical arbitrage is always used as primary strategy
    
    def _apply_portfolio_limits(self, signals: List[Dict]) -> List[Dict]:
        """Apply portfolio-level position and risk limits"""
        if not signals:
            return signals
        
        # Calculate total signal value
        total_signal_value = sum(
            signal.get('quantity', 0) * signal.get('price', 0) 
            for signal in signals
        )
        
        # Limit total exposure to 60% of portfolio
        max_exposure = self.initial_capital * 0.60
        if total_signal_value > max_exposure:
            scale_factor = max_exposure / total_signal_value
            for signal in signals:
                signal['quantity'] *= scale_factor
        
        return signals
    
    async def update_price(self, symbol: str, price: float):
        """Update price data for all strategies"""
        await self.stat_arb_strategy.update_price(symbol, price)
        await self.market_making_strategy.update_market_data(
            symbol, price * 0.999, price * 1.001, price, 10000
        )
        # Update HF momentum with tick data simulation
        from high_frequency_momentum_strategy import TickData
        tick = TickData(
            symbol=symbol,
            price=price,
            volume=1000,
            timestamp=datetime.now(),
            bid=price * 0.999,
            ask=price * 1.001,
            bid_size=1000,
            ask_size=1000
        )
        await self.hf_momentum_strategy.process_tick(tick)
    
    def calculate_performance_metrics(self) -> Dict:
        """Calculate comprehensive portfolio performance metrics"""
        if len(self.daily_returns) < 2:
            return {
                'total_return': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0,
                'win_rate': 0.0,
                'total_trades': self.total_trades
            }
        
        returns = np.array(self.daily_returns)
        
        total_return = (1 + returns).prod() - 1
        sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0
        
        # Calculate maximum drawdown from portfolio value history
        if len(self.portfolio_value_history) > 1:
            values = np.array(self.portfolio_value_history)
            peaks = np.maximum.accumulate(values)
            drawdowns = (peaks - values) / peaks
            max_drawdown = np.max(drawdowns)
        else:
            max_drawdown = 0.0
        
        win_rate = self.winning_trades / max(1, self.total_trades)
        
        return {
            'total_return': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'volatility': np.std(returns) * np.sqrt(252),
            'total_trades': self.total_trades,
            'avg_daily_return': np.mean(returns),
            'current_drawdown': self._calculate_current_drawdown()
        }
    
    async def on_trade_executed(self, trade_info: Dict):
        """Handle trade execution callback"""
        self.total_trades += 1
        
        if trade_info.get('pnl', 0) > 0:
            self.winning_trades += 1
        
        # Update daily PnL
        pnl = trade_info.get('pnl', 0)
        self.current_daily_pnl += pnl
        
        # Update portfolio value
        if self.portfolio_value_history:
            new_value = self.portfolio_value_history[-1] + pnl
            self.portfolio_value_history.append(new_value)
        
        # Forward to individual strategies
        strategy_type = trade_info.get('portfolio_strategy', '')
        if strategy_type == 'statistical_arbitrage':
            await self.stat_arb_strategy.on_trade_executed(trade_info)
        elif strategy_type == 'market_making':
            await self.market_making_strategy.on_trade_executed(trade_info)
        elif strategy_type == 'hf_momentum':
            await self.hf_momentum_strategy.on_trade_executed(trade_info)
    
    async def on_day_end(self):
        """Handle end of trading day"""
        if self.current_daily_pnl != 0:
            daily_return = self.current_daily_pnl / self.initial_capital
            self.daily_returns.append(daily_return)
        
        # Reset daily tracking
        self.current_daily_pnl = 0.0
        
        # Forward to individual strategies
        await self.stat_arb_strategy.on_day_end()
        await self.market_making_strategy.on_day_end()
        await self.hf_momentum_strategy.on_day_end()
        
        # Log daily performance
        metrics = self.calculate_performance_metrics()
        self.logger.info(f"Daily portfolio performance: {metrics}")

# Example usage
async def test_optimized_portfolio():
    """Test the optimized portfolio strategy"""
    symbols = ['BONK', 'WIF', 'POPCAT', 'FARTCOIN', 'SOL']
    portfolio = OptimizedPortfolioStrategy(symbols)
    
    await portfolio.initialize()
    
    # Simulate price updates and signal generation
    for i in range(100):
        for symbol in symbols:
            base_prices = {
                'BONK': 0.000035, 'WIF': 2.20, 'POPCAT': 1.15, 
                'FARTCOIN': 1.28, 'SOL': 150.0
            }
            price = base_prices[symbol] * (1 + np.random.normal(0, 0.01))
            await portfolio.update_price(symbol, price)
        
        signals = await portfolio.generate_signals()
        if signals:
            print(f"Generated {len(signals)} portfolio signals")
        
        await asyncio.sleep(0.1)
    
    # Print performance
    metrics = portfolio.calculate_performance_metrics()
    print(f"Portfolio Performance: {metrics}")

if __name__ == "__main__":
    asyncio.run(test_optimized_portfolio())
