"""
Final Portfolio Backtest
Comprehensive backtesting of the optimized portfolio strategy
Target: >50% return, >1.0 Sharpe ratio, <10% max drawdown, >50% win rate
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List
import logging

from optimized_portfolio_strategy import OptimizedPortfolioStrategy
from statistical_arbitrage_strategy import StatisticalArbitrageStrategy
from production_backtesting_system import ProductionBacktestingSystem

class FinalPortfolioBacktest:
    """Comprehensive backtesting system for optimized portfolio"""
    
    def __init__(self, initial_capital: float = 1000000):
        self.initial_capital = initial_capital
        self.symbols = ['BONK', 'WIF', 'POPCAT', 'FARTCOIN', 'SOL']
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger('FinalPortfolioBacktest')
        
    def generate_enhanced_market_data(self, num_days: int = 30) -> Dict[str, pd.DataFrame]:
        """Generate enhanced market data with realistic correlations and volatility"""
        
        # Base parameters for each symbol with realistic correlations
        symbol_params = {
            'BONK': {'base_price': 0.000035, 'volatility': 0.08, 'trend': 0.0001},
            'WIF': {'base_price': 2.20, 'volatility': 0.06, 'trend': 0.00005},
            'POPCAT': {'base_price': 1.15, 'volatility': 0.12, 'trend': 0.0002},
            'FARTCOIN': {'base_price': 1.28, 'volatility': 0.10, 'trend': -0.00005},
            'SOL': {'base_price': 150.0, 'volatility': 0.05, 'trend': 0.0001}
        }
        
        # Generate correlated price movements
        num_points = num_days * 144  # 10-minute intervals
        timestamps = pd.date_range(start='2024-01-01', periods=num_points, freq='10min')
        
        # Common market factor for correlation
        market_factor = np.random.normal(0, 0.01, num_points)
        
        market_data = {}
        
        for symbol, params in symbol_params.items():
            # Individual price movements
            individual_returns = np.random.normal(
                params['trend'], 
                params['volatility'] / np.sqrt(144), 
                num_points
            )
            
            # Combine market factor with individual movements
            correlation_strength = 0.3  # 30% correlation with market
            total_returns = (
                correlation_strength * market_factor + 
                (1 - correlation_strength) * individual_returns
            )
            
            # Generate price series
            prices = [params['base_price']]
            for ret in total_returns:
                new_price = prices[-1] * (1 + ret)
                prices.append(max(new_price, params['base_price'] * 0.1))  # Floor price
            
            prices = prices[1:]  # Remove initial price
            
            # Create DataFrame
            df = pd.DataFrame({
                'timestamp': timestamps,
                'price': prices,
                'volume': np.random.uniform(10000, 100000, num_points),
                'returns': total_returns
            })
            
            # Add bid-ask spread
            df['spread_bps'] = np.random.uniform(5, 25, len(df))
            half_spread = df['spread_bps'] / 20000
            df['bid'] = df['price'] * (1 - half_spread)
            df['ask'] = df['price'] * (1 + half_spread)
            
            market_data[symbol] = df
        
        return market_data
    
    async def run_portfolio_backtest(self) -> Dict:
        """Run comprehensive portfolio backtest"""
        
        self.logger.info("Starting Final Portfolio Backtest")
        
        # Generate market data
        market_data = self.generate_enhanced_market_data(30)
        
        # Initialize optimized portfolio
        portfolio = OptimizedPortfolioStrategy(self.symbols, self.initial_capital)
        await portfolio.initialize()
        
        # Initialize individual Statistical Arbitrage for comparison
        stat_arb_solo = StatisticalArbitrageStrategy(self.symbols, self.initial_capital)
        
        # Backtest variables
        portfolio_trades = []
        stat_arb_trades = []
        portfolio_daily_pnl = []
        stat_arb_daily_pnl = []
        
        portfolio_capital = self.initial_capital
        stat_arb_capital = self.initial_capital
        
        # Run backtest
        total_points = len(market_data[self.symbols[0]])
        
        for i in range(total_points):
            # Update prices for all strategies
            for symbol in self.symbols:
                price = market_data[symbol].iloc[i]['price']
                await portfolio.update_price(symbol, price)
                await stat_arb_solo.update_price(symbol, price)
            
            # Generate signals
            portfolio_signals = await portfolio.generate_signals()
            stat_arb_signals = await stat_arb_solo.generate_signals()
            
            # Simulate trade execution for portfolio
            for signal in portfolio_signals:
                trade_pnl = self._simulate_trade_execution(signal, market_data)
                if trade_pnl is not None:
                    portfolio_trades.append({
                        'timestamp': market_data[signal['symbol']].iloc[i]['timestamp'],
                        'symbol': signal['symbol'],
                        'pnl': trade_pnl,
                        'strategy': signal.get('portfolio_strategy', 'unknown')
                    })
                    portfolio_capital += trade_pnl
                    await portfolio.on_trade_executed({'pnl': trade_pnl})
            
            # Simulate trade execution for stat arb solo
            for signal in stat_arb_signals:
                trade_pnl = self._simulate_trade_execution(signal, market_data)
                if trade_pnl is not None:
                    stat_arb_trades.append({
                        'timestamp': market_data[signal['symbol']].iloc[i]['timestamp'],
                        'symbol': signal['symbol'],
                        'pnl': trade_pnl
                    })
                    stat_arb_capital += trade_pnl
                    await stat_arb_solo.on_trade_executed({'pnl': trade_pnl})
            
            # Daily PnL tracking (every 144 points = 1 day)
            if i % 144 == 0 and i > 0:
                portfolio_daily_return = (portfolio_capital - self.initial_capital) / self.initial_capital
                stat_arb_daily_return = (stat_arb_capital - self.initial_capital) / self.initial_capital
                
                portfolio_daily_pnl.append(portfolio_daily_return)
                stat_arb_daily_pnl.append(stat_arb_daily_return)
                
                await portfolio.on_day_end()
                await stat_arb_solo.on_day_end()
        
        # Calculate final performance metrics
        portfolio_metrics = self._calculate_performance_metrics(
            portfolio_trades, portfolio_daily_pnl, portfolio_capital
        )
        
        stat_arb_metrics = self._calculate_performance_metrics(
            stat_arb_trades, stat_arb_daily_pnl, stat_arb_capital
        )
        
        return {
            'optimized_portfolio': portfolio_metrics,
            'statistical_arbitrage_solo': stat_arb_metrics,
            'comparison': self._compare_strategies(portfolio_metrics, stat_arb_metrics)
        }
    
    def _simulate_trade_execution(self, signal: Dict, market_data: Dict) -> float:
        """Simulate trade execution and return PnL"""
        try:
            symbol = signal['symbol']
            quantity = signal.get('quantity', 0)
            side = signal.get('side', 'buy')
            
            if quantity <= 0:
                return None
            
            # Simple execution simulation with slippage
            slippage = 0.001  # 10 basis points slippage
            
            if side == 'buy':
                execution_price = market_data[symbol].iloc[-1]['ask'] * (1 + slippage)
                # Simulate holding for 10 periods and selling
                if len(market_data[symbol]) > 10:
                    exit_price = market_data[symbol].iloc[-1]['bid'] * (1 - slippage)
                    pnl = (exit_price - execution_price) * quantity
                else:
                    pnl = 0
            else:  # sell
                execution_price = market_data[symbol].iloc[-1]['bid'] * (1 - slippage)
                if len(market_data[symbol]) > 10:
                    exit_price = market_data[symbol].iloc[-1]['ask'] * (1 + slippage)
                    pnl = (execution_price - exit_price) * quantity
                else:
                    pnl = 0
            
            return pnl
            
        except Exception as e:
            return None
    
    def _calculate_performance_metrics(self, trades: List[Dict], daily_pnl: List[float], 
                                     final_capital: float) -> Dict:
        """Calculate comprehensive performance metrics"""
        
        if not trades:
            return {
                'total_return': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0,
                'win_rate': 0.0,
                'total_trades': 0
            }
        
        # Basic metrics
        total_return = (final_capital - self.initial_capital) / self.initial_capital
        total_trades = len(trades)
        
        # Win rate
        winning_trades = [t for t in trades if t['pnl'] > 0]
        win_rate = len(winning_trades) / total_trades if total_trades > 0 else 0
        
        # Sharpe ratio
        if daily_pnl and len(daily_pnl) > 1:
            returns = np.array(daily_pnl)
            mean_return = np.mean(returns)
            std_return = np.std(returns)
            sharpe_ratio = (mean_return / std_return) * np.sqrt(252) if std_return > 0 else 0
        else:
            sharpe_ratio = 0
        
        # Maximum drawdown
        if daily_pnl:
            cumulative_returns = np.cumprod(1 + np.array(daily_pnl))
            peaks = np.maximum.accumulate(cumulative_returns)
            drawdowns = (peaks - cumulative_returns) / peaks
            max_drawdown = np.max(drawdowns) if len(drawdowns) > 0 else 0
        else:
            max_drawdown = 0
        
        return {
            'total_return': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'total_trades': total_trades,
            'final_capital': final_capital,
            'avg_trade_pnl': np.mean([t['pnl'] for t in trades]) if trades else 0
        }
    
    def _compare_strategies(self, portfolio_metrics: Dict, stat_arb_metrics: Dict) -> Dict:
        """Compare portfolio vs individual strategy performance"""
        
        return {
            'return_improvement': portfolio_metrics['total_return'] - stat_arb_metrics['total_return'],
            'sharpe_improvement': portfolio_metrics['sharpe_ratio'] - stat_arb_metrics['sharpe_ratio'],
            'drawdown_improvement': stat_arb_metrics['max_drawdown'] - portfolio_metrics['max_drawdown'],
            'win_rate_improvement': portfolio_metrics['win_rate'] - stat_arb_metrics['win_rate'],
            'recommended_strategy': 'portfolio' if portfolio_metrics['total_return'] > stat_arb_metrics['total_return'] 
                                   and portfolio_metrics['max_drawdown'] < stat_arb_metrics['max_drawdown'] 
                                   else 'statistical_arbitrage'
        }
    
    def print_results(self, results: Dict):
        """Print comprehensive backtest results"""
        
        print("\n" + "="*80)
        print("FINAL PORTFOLIO BACKTEST RESULTS")
        print("="*80)
        
        portfolio_metrics = results['optimized_portfolio']
        stat_arb_metrics = results['statistical_arbitrage_solo']
        comparison = results['comparison']
        
        print(f"\nOPTIMIZED PORTFOLIO PERFORMANCE:")
        print(f"  Total Return: {portfolio_metrics['total_return']:.2%}")
        print(f"  Sharpe Ratio: {portfolio_metrics['sharpe_ratio']:.3f}")
        print(f"  Max Drawdown: {portfolio_metrics['max_drawdown']:.2%}")
        print(f"  Win Rate: {portfolio_metrics['win_rate']:.1%}")
        print(f"  Total Trades: {portfolio_metrics['total_trades']}")
        
        print(f"\nSTATISTICAL ARBITRAGE SOLO PERFORMANCE:")
        print(f"  Total Return: {stat_arb_metrics['total_return']:.2%}")
        print(f"  Sharpe Ratio: {stat_arb_metrics['sharpe_ratio']:.3f}")
        print(f"  Max Drawdown: {stat_arb_metrics['max_drawdown']:.2%}")
        print(f"  Win Rate: {stat_arb_metrics['win_rate']:.1%}")
        print(f"  Total Trades: {stat_arb_metrics['total_trades']}")
        
        print(f"\nTARGET METRICS ACHIEVEMENT:")
        print(f"  Return Target (>50%): {'✅' if portfolio_metrics['total_return'] > 0.5 else '❌'} ({portfolio_metrics['total_return']:.1%})")
        print(f"  Sharpe Target (>1.0): {'✅' if portfolio_metrics['sharpe_ratio'] > 1.0 else '❌'} ({portfolio_metrics['sharpe_ratio']:.2f})")
        print(f"  Drawdown Target (<10%): {'✅' if portfolio_metrics['max_drawdown'] < 0.1 else '❌'} ({portfolio_metrics['max_drawdown']:.1%})")
        print(f"  Win Rate Target (>50%): {'✅' if portfolio_metrics['win_rate'] > 0.5 else '❌'} ({portfolio_metrics['win_rate']:.1%})")
        
        targets_met = sum([
            portfolio_metrics['total_return'] > 0.5,
            portfolio_metrics['sharpe_ratio'] > 1.0,
            portfolio_metrics['max_drawdown'] < 0.1,
            portfolio_metrics['win_rate'] > 0.5
        ])
        
        print(f"\nOVERALL TARGET ACHIEVEMENT: {'✅ SUCCESS' if targets_met >= 3 else '❌ NEEDS IMPROVEMENT'} ({targets_met}/4 targets met)")
        print(f"RECOMMENDED STRATEGY: {comparison['recommended_strategy'].upper()}")

async def main():
    """Run the final portfolio backtest"""
    backtester = FinalPortfolioBacktest()
    results = await backtester.run_portfolio_backtest()
    backtester.print_results(results)

if __name__ == "__main__":
    asyncio.run(main())
