"""
On-Chain Momentum Following Strategy
Follow smart money flows using blockchain transaction analysis
Target: >50% return, >1.0 Sharpe ratio, <10% max drawdown, >50% win rate
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import logging

@dataclass
class SmartMoneySignal:
    """Smart money flow signal from on-chain analysis"""
    symbol: str
    wallet_address: str
    transaction_type: str  # 'buy', 'sell', 'accumulate', 'distribute'
    amount_usd: float
    wallet_pnl_30d: float  # 30-day P&L of the wallet
    wallet_win_rate: float  # Historical win rate of the wallet
    confidence: float  # Signal confidence based on wallet performance
    flow_strength: float  # Strength of the money flow
    timestamp: datetime

class OnChainMomentumStrategy:
    """
    On-Chain Momentum Following Strategy
    
    Based on successful on-chain traders identified through platforms like:
    - GMGN.ai: Top performing wallet tracking
    - Dune Analytics: Smart money flow analysis
    - Arkham Intelligence: Whale wallet monitoring
    
    Key Features:
    - Smart money wallet identification and tracking
    - Transaction pattern analysis
    - Momentum following with size-based filtering
    - Risk management with wallet performance weighting
    """
    
    def __init__(self, symbols: List[str], initial_capital: float = 1000000):
        self.symbols = symbols
        self.initial_capital = initial_capital
        self.strategy_id = "onchain_momentum_v1"
        
        # Strategy parameters - Based on successful on-chain traders
        self.min_transaction_size = 50000  # Minimum $50K transaction to follow
        self.min_wallet_pnl_30d = 0.20  # Minimum 20% 30-day P&L for wallet
        self.min_wallet_win_rate = 0.60  # Minimum 60% win rate for wallet
        self.max_position_size = initial_capital * 0.12  # 12% max position per symbol
        self.max_portfolio_exposure = initial_capital * 0.70  # 70% max total exposure
        
        # Smart money wallet database (simulated - in production, connect to APIs)
        self.smart_wallets = self._initialize_smart_wallets()
        
        # Signal filtering and timing
        self.signal_decay_hours = 4  # Signals decay after 4 hours
        self.min_signal_confidence = 0.7  # Minimum 70% confidence
        self.follow_delay_minutes = 5  # 5-minute delay to avoid front-running
        
        # Risk management
        self.max_daily_loss = initial_capital * 0.03  # 3% daily loss limit
        self.position_hold_hours = 24  # Maximum 24 hours hold time
        self.stop_loss_pct = 0.08  # 8% stop loss
        self.take_profit_pct = 0.15  # 15% take profit
        
        # Transaction costs (realistic for following smart money)
        self.transaction_cost_bps = 30  # 30 basis points (higher due to timing)
        self.slippage_bps = 20  # 20 basis points slippage
        
        # Performance tracking
        self.active_positions = {}
        self.completed_trades = []
        self.daily_returns = []
        self.total_pnl = 0.0
        self.current_daily_pnl = 0.0
        self.trades_executed = 0
        self.win_count = 0
        self.loss_count = 0
        
        # Signal tracking
        self.recent_signals = []
        self.wallet_performance_cache = {}
        
        # Market data
        self.price_history = {symbol: [] for symbol in symbols}
        
        # Logging
        self.logger = logging.getLogger(f'OnChainMomentum_{self.strategy_id}')
        
    def _initialize_smart_wallets(self) -> Dict[str, Dict]:
        """Initialize database of smart money wallets (simulated)"""
        # In production, this would connect to GMGN.ai, Dune Analytics APIs
        return {
            'whale_1': {
                'address': '0x1234...abcd',
                'pnl_30d': 0.45,  # 45% 30-day P&L
                'win_rate': 0.72,  # 72% win rate
                'avg_position_size': 200000,  # $200K average position
                'specialization': ['BONK', 'WIF', 'POPCAT'],
                'reputation_score': 0.9
            },
            'smart_trader_2': {
                'address': '0x5678...efgh',
                'pnl_30d': 0.38,  # 38% 30-day P&L
                'win_rate': 0.68,  # 68% win rate
                'avg_position_size': 150000,  # $150K average position
                'specialization': ['SOL', 'FARTCOIN'],
                'reputation_score': 0.85
            },
            'momentum_master': {
                'address': '0x9abc...ijkl',
                'pnl_30d': 0.52,  # 52% 30-day P&L
                'win_rate': 0.75,  # 75% win rate
                'avg_position_size': 300000,  # $300K average position
                'specialization': ['WIF', 'SOL', 'POPCAT'],
                'reputation_score': 0.95
            }
        }
    
    async def update_price(self, symbol: str, price: float):
        """Update price data"""
        self.price_history[symbol].append(price)
        
        # Keep only last 1000 prices
        if len(self.price_history[symbol]) > 1000:
            self.price_history[symbol] = self.price_history[symbol][-1000:]
    
    def _simulate_onchain_transaction(self, symbol: str) -> Optional[SmartMoneySignal]:
        """Simulate on-chain transaction detection (in production, use real APIs)"""
        
        # Randomly generate smart money transactions based on market conditions
        if np.random.random() > 0.95:  # 5% chance of smart money transaction
            
            # Select random smart wallet
            wallet_id = np.random.choice(list(self.smart_wallets.keys()))
            wallet_data = self.smart_wallets[wallet_id]
            
            # Check if wallet specializes in this symbol
            if symbol not in wallet_data['specialization']:
                return None
            
            # Generate transaction details
            transaction_types = ['buy', 'sell', 'accumulate', 'distribute']
            weights = [0.4, 0.3, 0.2, 0.1]  # Bias towards buying
            transaction_type = np.random.choice(transaction_types, p=weights)
            
            # Transaction size based on wallet's typical size
            base_size = wallet_data['avg_position_size']
            size_multiplier = np.random.uniform(0.5, 2.0)
            amount_usd = base_size * size_multiplier
            
            # Only consider large transactions
            if amount_usd < self.min_transaction_size:
                return None
            
            # Calculate signal confidence based on wallet performance
            pnl_score = min(1.0, wallet_data['pnl_30d'] / 0.5)  # Normalize to 50% max
            win_rate_score = wallet_data['win_rate']
            reputation_score = wallet_data['reputation_score']
            
            confidence = (pnl_score * 0.4 + win_rate_score * 0.4 + reputation_score * 0.2)
            
            # Flow strength based on transaction size and wallet reputation
            flow_strength = min(1.0, (amount_usd / 100000) * reputation_score)
            
            return SmartMoneySignal(
                symbol=symbol,
                wallet_address=wallet_data['address'],
                transaction_type=transaction_type,
                amount_usd=amount_usd,
                wallet_pnl_30d=wallet_data['pnl_30d'],
                wallet_win_rate=wallet_data['win_rate'],
                confidence=confidence,
                flow_strength=flow_strength,
                timestamp=datetime.now()
            )
        
        return None
    
    def _filter_signals(self, signals: List[SmartMoneySignal]) -> List[SmartMoneySignal]:
        """Filter signals based on quality criteria"""
        filtered = []
        
        for signal in signals:
            # Check minimum wallet performance
            if (signal.wallet_pnl_30d >= self.min_wallet_pnl_30d and
                signal.wallet_win_rate >= self.min_wallet_win_rate and
                signal.confidence >= self.min_signal_confidence):
                
                # Check signal freshness
                age_hours = (datetime.now() - signal.timestamp).total_seconds() / 3600
                if age_hours <= self.signal_decay_hours:
                    filtered.append(signal)
        
        # Sort by confidence and flow strength
        filtered.sort(key=lambda x: x.confidence * x.flow_strength, reverse=True)
        
        return filtered
    
    async def generate_signals(self) -> List[Dict]:
        """Generate on-chain momentum following signals"""
        
        # Check risk limits
        if self.current_daily_pnl < -self.max_daily_loss:
            self.logger.warning("Daily loss limit exceeded")
            return []
        
        # Simulate on-chain transaction detection
        new_signals = []
        for symbol in self.symbols:
            signal = self._simulate_onchain_transaction(symbol)
            if signal:
                new_signals.append(signal)
        
        # Add to recent signals
        self.recent_signals.extend(new_signals)
        
        # Remove old signals
        current_time = datetime.now()
        self.recent_signals = [
            s for s in self.recent_signals 
            if (current_time - s.timestamp).total_seconds() / 3600 <= self.signal_decay_hours
        ]
        
        # Filter high-quality signals
        quality_signals = self._filter_signals(self.recent_signals)
        
        trading_signals = []
        
        for signal in quality_signals[:3]:  # Top 3 signals
            symbol = signal.symbol
            
            # Skip if already have position
            if symbol in self.active_positions:
                continue
            
            # Only follow buy/accumulate signals for momentum
            if signal.transaction_type not in ['buy', 'accumulate']:
                continue
            
            # Calculate position size based on signal strength and wallet performance
            base_size = min(signal.amount_usd * 0.1, self.max_position_size)  # 10% of smart money size
            confidence_adjusted_size = base_size * signal.confidence
            flow_adjusted_size = confidence_adjusted_size * signal.flow_strength
            
            position_size = min(flow_adjusted_size, self.max_position_size)
            
            if position_size < self.initial_capital * 0.01:  # Minimum 1% position
                continue
            
            current_price = self.price_history[symbol][-1] if self.price_history[symbol] else 1.0
            quantity = position_size / current_price
            
            # Add execution delay to avoid front-running
            execution_delay = np.random.uniform(3, 8)  # 3-8 minutes delay
            
            trading_signals.append({
                'symbol': symbol,
                'side': 'buy',
                'order_type': 'market',
                'quantity': quantity,
                'price': current_price,
                'strategy_type': 'onchain_momentum',
                'signal_confidence': signal.confidence,
                'flow_strength': signal.flow_strength,
                'wallet_pnl_30d': signal.wallet_pnl_30d,
                'wallet_win_rate': signal.wallet_win_rate,
                'smart_money_size': signal.amount_usd,
                'execution_delay': execution_delay
            })
            
            self.logger.info(f"Smart money signal: {symbol} "
                           f"Wallet P&L: {signal.wallet_pnl_30d:.1%} "
                           f"Win Rate: {signal.wallet_win_rate:.1%} "
                           f"Size: ${signal.amount_usd:,.0f} "
                           f"Confidence: {signal.confidence:.2f}")
        
        return trading_signals
    
    async def on_trade_executed(self, trade_info: Dict):
        """Handle trade execution callback"""
        symbol = trade_info.get('symbol')
        quantity = trade_info.get('quantity', 0)
        price = trade_info.get('price', 0)
        
        # Track active position
        self.active_positions[symbol] = {
            'side': 'buy',
            'quantity': quantity,
            'entry_price': price,
            'entry_time': datetime.now(),
            'signal_confidence': trade_info.get('signal_confidence', 0.7),
            'wallet_performance': trade_info.get('wallet_pnl_30d', 0.3)
        }
        
        self.trades_executed += 1
        self.logger.info(f"Following smart money: {symbol} {quantity:.2f} @ {price:.6f}")
    
    async def manage_positions(self):
        """Manage active positions with stops and time-based exits"""
        positions_to_close = []
        
        for symbol, position in self.active_positions.items():
            if not self.price_history[symbol]:
                continue
            
            current_price = self.price_history[symbol][-1]
            entry_price = position['entry_price']
            
            # Calculate current P&L
            pnl_pct = (current_price - entry_price) / entry_price
            
            # Check stop loss
            if pnl_pct <= -self.stop_loss_pct:
                positions_to_close.append((symbol, 'stop_loss', pnl_pct))
                continue
            
            # Check take profit
            if pnl_pct >= self.take_profit_pct:
                positions_to_close.append((symbol, 'take_profit', pnl_pct))
                continue
            
            # Check time-based exit
            hours_held = (datetime.now() - position['entry_time']).total_seconds() / 3600
            if hours_held >= self.position_hold_hours:
                positions_to_close.append((symbol, 'time_exit', pnl_pct))
                continue
            
            # Dynamic exit based on wallet performance
            # If following a high-performance wallet, hold longer
            wallet_perf = position['wallet_performance']
            if wallet_perf > 0.4 and pnl_pct > 0.05:  # 40%+ wallet performance, 5%+ profit
                # Hold for potential larger gains
                continue
        
        # Close positions
        for symbol, reason, pnl_pct in positions_to_close:
            await self._close_position(symbol, reason, pnl_pct)
    
    async def _close_position(self, symbol: str, reason: str, pnl_pct: float):
        """Close an active position"""
        if symbol not in self.active_positions:
            return
        
        position = self.active_positions[symbol]
        position_value = position['quantity'] * position['entry_price']
        
        # Calculate P&L after transaction costs
        gross_pnl = position_value * pnl_pct
        transaction_costs = position_value * ((self.transaction_cost_bps + self.slippage_bps) / 10000)
        net_pnl = gross_pnl - transaction_costs
        
        self.total_pnl += net_pnl
        self.current_daily_pnl += net_pnl
        
        if net_pnl > 0:
            self.win_count += 1
        else:
            self.loss_count += 1
        
        # Store trade record
        self.completed_trades.append({
            'timestamp': datetime.now(),
            'symbol': symbol,
            'quantity': position['quantity'],
            'entry_price': position['entry_price'],
            'exit_price': self.price_history[symbol][-1],
            'pnl': net_pnl,
            'pnl_pct': pnl_pct,
            'reason': reason,
            'signal_confidence': position['signal_confidence'],
            'wallet_performance': position['wallet_performance']
        })
        
        del self.active_positions[symbol]
        
        self.logger.info(f"Position closed: {symbol} {reason} "
                        f"P&L: {pnl_pct:.2%} (${net_pnl:.2f})")
    
    async def on_day_end(self):
        """Handle end of trading day"""
        # Manage positions
        await self.manage_positions()
        
        if self.current_daily_pnl != 0:
            daily_return = self.current_daily_pnl / self.initial_capital
            self.daily_returns.append(daily_return)
        
        self.current_daily_pnl = 0.0
    
    def calculate_performance_metrics(self) -> Dict:
        """Calculate strategy performance metrics"""
        if len(self.daily_returns) < 2:
            return {
                'total_return': 0.0,
                'sharpe_ratio': 0.0,
                'max_drawdown': 0.0,
                'win_rate': 0.0,
                'total_trades': self.trades_executed
            }
        
        returns = np.array(self.daily_returns)
        
        total_return = (1 + returns).prod() - 1
        sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0
        
        # Calculate maximum drawdown
        cumulative_returns = (1 + returns).cumprod()
        peak = np.maximum.accumulate(cumulative_returns)
        drawdown = (cumulative_returns - peak) / peak
        max_drawdown = np.min(drawdown)
        
        total_closed_trades = self.win_count + self.loss_count
        win_rate = self.win_count / total_closed_trades if total_closed_trades > 0 else 0
        
        return {
            'total_return': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': abs(max_drawdown),
            'win_rate': win_rate,
            'total_trades': self.trades_executed,
            'avg_profit_per_trade': self.total_pnl / max(1, total_closed_trades),
            'total_pnl': self.total_pnl,
            'active_positions': len(self.active_positions)
        }

# Test function
async def test_onchain_momentum():
    """Test the on-chain momentum strategy"""
    symbols = ['BONK', 'WIF', 'POPCAT', 'FARTCOIN', 'SOL']
    strategy = OnChainMomentumStrategy(symbols)
    
    # Simulate price updates and smart money activity
    for i in range(1000):  # 1000 time steps
        for symbol in symbols:
            base_prices = {
                'BONK': 0.000035, 'WIF': 2.20, 'POPCAT': 1.15,
                'FARTCOIN': 1.28, 'SOL': 150.0
            }
            
            # Simulate price movements with occasional smart money impact
            if i == 0:
                new_price = base_prices[symbol]
            else:
                last_price = strategy.price_history[symbol][-1]
                # Normal price movement
                price_change = np.random.normal(0, 0.02)
                new_price = last_price * (1 + price_change)
            
            await strategy.update_price(symbol, new_price)
        
        # Generate and execute signals
        signals = await strategy.generate_signals()
        for signal in signals:
            await strategy.on_trade_executed(signal)
        
        # Manage positions
        await strategy.manage_positions()
        
        # End of day every 144 steps
        if i % 144 == 0 and i > 0:
            await strategy.on_day_end()
    
    # Print performance
    metrics = strategy.calculate_performance_metrics()
    print(f"On-Chain Momentum Performance: {metrics}")

if __name__ == "__main__":
    asyncio.run(test_onchain_momentum())
